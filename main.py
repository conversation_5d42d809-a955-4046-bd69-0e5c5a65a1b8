import pywintypes
import random
import time
import tkinter
import win32api
import win32con


# import pyautogui  # 暂时注释掉，使用pynput替代
from pynput import keyboard
from pynput import mouse

# 创建键盘控制器
keyboard_controller = keyboard.Controller()


label = tkinter.Label(text='Holding', font=('Times', '50'), fg='blue', bg='white')
label.master.overrideredirect(True)
label.master.geometry("+250+250")
label.master.lift()
label.master.wm_attributes("-topmost", True)
label.master.wm_attributes("-disabled", True)
label.master.wm_attributes("-transparentcolor", "white")
hWindow = pywintypes.HANDLE(int(label.master.frame(), 16))
# http://msdn.microsoft.com/en-us/library/windows/desktop/ff700543(v=vs.85).aspx
# The WS_EX_TRANSPARENT flag makes events (like mouse clicks) fall through the window.
exStyle = win32con.WS_EX_COMPOSITED | win32con.WS_EX_LAYERED | win32con.WS_EX_NOACTIVATE | win32con.WS_EX_TOPMOST | win32con.WS_EX_TRANSPARENT
win32api.SetWindowLong(hWindow, win32con.GWL_EXSTYLE, exStyle)
label.pack()

timerRunning = 0
key_2 = 0
key_3 = 0
key_q = 0
key_f1 = 0

# 按键序列相关变量
key_sequence = ['1', '2', '3', '4', '5', '6', 'space']
current_key_index = 0


# 替代pyautogui.press()函数
def press_key(key):
    try:
        # 处理特殊键
        if key == 'space':
            keyboard_controller.press(keyboard.Key.space)
            keyboard_controller.release(keyboard.Key.space)
        else:
            # 处理常规按键
            keyboard_controller.press(key)
            keyboard_controller.release(key)
        print(f"按下按键: {key}")
    except Exception as e:
        print(f"按键模拟失败: {key}, 错误: {e}")


def weighted_random_from_range(weighted_ranges):
    # 根据权重选择一个范围
    selected_range, _ = random.choices(weighted_ranges, weights=[w for _, w in weighted_ranges], k=1)[0]

    # 在选定的范围内生成随机整数
    start, end = selected_range
    return random.randint(start, end)


def GetColor(x, y):
    # 暂时返回固定值，因为pyautogui有问题
    # color = pyautogui.pixel(x, y)
    # # Convert color to hex and take the upper 4 digits
    # color_hex = ''.join([format(c, '02X') for c in color])[-4:]
    # return color_hex
    return "0000"  # 临时返回值


def ColorCheck(x, y, ref):
    if (GetColor(x, y) == ref and GetColor(x - 3, y) == ref and GetColor(x + 3, y) == ref and
            GetColor(x, y - 3) == ref and GetColor(x, y + 3) == ref):
        return [x, y]
    else:
        return False


def press_key_sequence():
    """按顺序按下1,2,3,4,5,6,space键"""
    global current_key_index

    # 获取当前要按的键
    key_to_press = key_sequence[current_key_index]

    # 按下键
    press_key(key_to_press)

    # 移动到下一个键
    current_key_index = (current_key_index + 1) % len(key_sequence)


def Paladin_Retribution():
    # 暂时注释掉原有功能，因为pyautogui有问题
    '''
    if (GetColor(610,939)=="1115") and (GetColor(607,939)=="090A") and (GetColor(613,939)=="0909") and (GetColor(610,936)=="0E0F") and (GetColor(610,942)=="1114"):
        press_key('q')
    if (GetColor(618, 970) == "345A") and (GetColor(615, 970) == "3161") and (GetColor(621, 970) == "427C") and (GetColor(618, 967) == "4161") and (GetColor(618, 973) == "4771"):
        press_key('q')
    if (GetColor(600, 961) == "1C1A") and (GetColor(597, 961) == "1D1A") and (GetColor(603, 961) == "201D") and (GetColor(600, 958) == "221E") and (GetColor(600, 964) == "1D19"):
        press_key('q')
    if (GetColor(613, 955) == "1E1C") and (GetColor(610, 955) == "1916") and (GetColor(616, 955) == "1916") and (
            GetColor(613, 952) == "1C1C") and (GetColor(613, 958) == "1B18"):
        press_key('q')
    '''
    return

def on_click(x, y, button, pressed):
    # 检查是否是中键被按下
    global timerRunning
    if button == mouse.Button.middle and pressed:
        if timerRunning == 0:
            timerRunning = 1
        else:
            timerRunning = 0
        print("timerRunning = " + str(timerRunning))

# 创建监听器
listener = mouse.Listener(
    on_click=on_click)
listener.start()

# 定义范围和对应的权重 (5-30ms)
ranges = [(5, 30)]
weights = [1.0]  # 权重总和应该为1

# 将范围和权重组合成一个列表
weighted_ranges = list(zip(ranges, weights))

while True:
    random_number = weighted_random_from_range(weighted_ranges)
    time.sleep(random_number / 1000)
    label.update()
    if timerRunning == 1:
        label.config(text="Running", fg='red')
        # 按键序列功能
        press_key_sequence()123456 123456 123456 123456 123456 123456 12
        # 原有的Paladin_Retribution功能（如果需要可以注释掉）
        # Paladin_Retribution()

    else:
        label.config(text="Holding", fg='blue')
