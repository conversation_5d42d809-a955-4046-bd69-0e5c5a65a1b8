import pywintypes
import random
import time
import tkinter
import win32api
import win32con


import pyautogui
from pynput import keyboard
from pynput import mouse


label = tkinter.Label(text='Holding', font=('Times', '50'), fg='blue', bg='white')
label.master.overrideredirect(True)
label.master.geometry("+250+250")
label.master.lift()
label.master.wm_attributes("-topmost", True)
label.master.wm_attributes("-disabled", True)
label.master.wm_attributes("-transparentcolor", "white")
hWindow = pywintypes.HANDLE(int(label.master.frame(), 16))
# http://msdn.microsoft.com/en-us/library/windows/desktop/ff700543(v=vs.85).aspx
# The WS_EX_TRANSPARENT flag makes events (like mouse clicks) fall through the window.
exStyle = win32con.WS_EX_COMPOSITED | win32con.WS_EX_LAYERED | win32con.WS_EX_NOACTIVATE | win32con.WS_EX_TOPMOST | win32con.WS_EX_TRANSPARENT
win32api.SetWindowLong(hWindow, win32con.GWL_EXSTYLE, exStyle)
label.pack()

timerRunning = 0
key_2 = 0
key_3 = 0
key_q = 0
key_f1 = 0


def weighted_random_from_range(weighted_ranges):
    # 根据权重选择一个范围
    selected_range, _ = random.choices(weighted_ranges, weights=[w for _, w in weighted_ranges], k=1)[0]

    # 在选定的范围内生成随机整数
    start, end = selected_range
    return random.randint(start, end)


def GetColor(x, y):
    color = pyautogui.pixel(x, y)
    # Convert color to hex and take the upper 4 digits
    color_hex = ''.join([format(c, '02X') for c in color])[-4:]
    return color_hex


def ColorCheck(x, y, ref):
    if (GetColor(x, y) == ref and GetColor(x - 3, y) == ref and GetColor(x + 3, y) == ref and
            GetColor(x, y - 3) == ref and GetColor(x, y + 3) == ref):
        return [x, y]
    else:
        return False


def Paladin_Retribution():

    if (GetColor(610,939)=="1115") and (GetColor(607,939)=="090A") and (GetColor(613,939)=="0909") and (GetColor(610,936)=="0E0F") and (GetColor(610,942)=="1114"):
        pyautogui.press('q')
    if (GetColor(618, 970) == "345A") and (GetColor(615, 970) == "3161") and (GetColor(621, 970) == "427C") and (GetColor(618, 967) == "4161") and (GetColor(618, 973) == "4771"):
        pyautogui.press('q')
    if (GetColor(600, 961) == "1C1A") and (GetColor(597, 961) == "1D1A") and (GetColor(603, 961) == "201D") and (GetColor(600, 958) == "221E") and (GetColor(600, 964) == "1D19"):
        pyautogui.press('q')
    if (GetColor(613, 955) == "1E1C") and (GetColor(610, 955) == "1916") and (GetColor(616, 955) == "1916") and (
            GetColor(613, 952) == "1C1C") and (GetColor(613, 958) == "1B18"):
        pyautogui.press('q')
        '''
    if not((GetColor(1310, 1008) == "3334") and (GetColor(1307, 1008) == "2D32") and (GetColor(1313, 1008) == "3333") and (
            GetColor(1310, 1005) == "2C2D") and (GetColor(1310, 1011) == "383C")):
        if (GetColor(681, 1040) == "3436") and (GetColor(678, 1040) == "4749") and (GetColor(684, 1040) == "4749") and (
                GetColor(681, 1037) == "4749") and (GetColor(681, 1043) == "1D22"):
            random_number = random.randint(1, 2)
            if random_number == 1:
                pyautogui.click(button='right')
'''
    return

def on_click(x, y, button, pressed):
    # 检查是否是中键被按下
    global timerRunning
    if button == mouse.Button.middle and pressed:
        if timerRunning == 0:
            timerRunning = 1
        else:
            timerRunning = 0
        print("timerRunning = " + str(timerRunning))

# 创建监听器
listener = mouse.Listener(
    on_click=on_click)
listener.start()

# 定义范围和对应的权重
ranges = [(11, 42), (43, 49)]
weights = [0.9, 0.1]  # 权重总和应该为1

# 将范围和权重组合成一个列表
weighted_ranges = list(zip(ranges, weights))

while True:
    random_number = weighted_random_from_range(weighted_ranges)
    time.sleep(random_number / 1000)
    label.update()
    if timerRunning == 1:
        label.config(text="Running", fg='red')
        Paladin_Retribution()

    else:
        label.config(text="Holding", fg='blue')
